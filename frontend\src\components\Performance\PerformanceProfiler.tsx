import React, { Profiler, ProfilerOnRenderCallback, useState, useEffect } from 'react';
import { logProfilerData } from '../../utils/performanceUtils';

interface PerformanceProfilerProps {
  id: string;
  children: React.ReactNode;
  enabled?: boolean;
  onRender?: ProfilerOnRenderCallback;
}

interface RenderMetrics {
  id: string;
  phase: 'mount' | 'update';
  actualDuration: number;
  baseDuration: number;
  startTime: number;
  commitTime: number;
  timestamp: number;
}

const PerformanceProfiler: React.FC<PerformanceProfilerProps> = ({
  id,
  children,
  enabled = process.env.NODE_ENV === 'development',
  onRender,
}) => {
  const [metrics, setMetrics] = useState<RenderMetrics[]>([]);

  const handleRender: ProfilerOnRenderCallback = (
    id,
    phase,
    actualDuration,
    baseDuration,
    startTime,
    commitTime,
    interactions
  ) => {
    // 记录性能数据
    const metric: RenderMetrics = {
      id,
      phase,
      actualDuration,
      baseDuration,
      startTime,
      commitTime,
      timestamp: Date.now(),
    };

    setMetrics(prev => [...prev.slice(-9), metric]); // 保留最近10次渲染数据

    // 输出到控制台
    logProfilerData(id, phase, actualDuration, baseDuration, startTime, commitTime);

    // 调用自定义回调
    if (onRender) {
      onRender(id, phase, actualDuration, baseDuration, startTime, commitTime, interactions);
    }

    // 性能警告
    if (actualDuration > 16) { // 超过一帧的时间
      console.warn(`⚠️ Slow render detected in ${id}: ${actualDuration.toFixed(2)}ms`);
    }
  };

  // 在开发环境中显示性能统计
  useEffect(() => {
    if (enabled && metrics.length > 0) {
      const avgDuration = metrics.reduce((sum, m) => sum + m.actualDuration, 0) / metrics.length;
      const maxDuration = Math.max(...metrics.map(m => m.actualDuration));
      
      console.log(`📊 Performance stats for ${id}:`, {
        averageRenderTime: `${avgDuration.toFixed(2)}ms`,
        maxRenderTime: `${maxDuration.toFixed(2)}ms`,
        totalRenders: metrics.length,
      });
    }
  }, [metrics, id, enabled]);

  if (!enabled) {
    return <>{children}</>;
  }

  return (
    <Profiler id={id} onRender={handleRender}>
      {children}
    </Profiler>
  );
};

export default PerformanceProfiler;
