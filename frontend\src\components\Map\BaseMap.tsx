import React, { useEffect, useRef, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// 修复Leaflet默认图标问题
delete (L.Icon.Default.prototype as unknown)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl:
    'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface BaseMapProps {
  center?: [number, number];
  zoom?: number;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  onMapReady?: (map: L.Map) => void;
}

// 地图事件处理组件 - 使用useCallback优化
const MapEventHandler: React.FC<{ onMapReady?: (map: L.Map) => void }> = ({
  onMapReady,
}) => {
  const map = useMap();
  const mapRef = useRef<L.Map | null>(null);

  const handleMapReady = useCallback(() => {
    if (onMapReady && map && mapRef.current !== map) {
      mapRef.current = map;
      onMapReady(map);
    }
  }, [map, onMapReady]);

  useEffect(() => {
    handleMapReady();
  }, [handleMapReady]);

  return null;
};

const BaseMap: React.FC<BaseMapProps> = ({
  center = [30.0, 120.0],
  zoom = 5,
  style = { height: '500px', width: '100%' },
  children,
  onMapReady,
}) => {


  return (
    <MapContainer
      center={center}
      zoom={zoom}
      style={style}
      scrollWheelZoom={true}
      zoomControl={true}
    >
      {/* 地图瓦片层 */}
      <TileLayer
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />

      {/* 地图事件处理 */}
      <MapEventHandler onMapReady={onMapReady} />

      {/* 子组件 */}
      {children}
    </MapContainer>
  );
};

export default BaseMap;
